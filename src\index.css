
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 14% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 14% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 14% 15%;

    --primary: 220 47% 11%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 220 10% 46%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    /* Real estate specific colors */
    --estate: 220 47% 11%;
    --estate-foreground: 210 40% 98%;
    --estate-muted: 210 40% 96.1%;
    --estate-muted-foreground: 220 10% 46%;
    --estate-accent: 217 76% 56%;
    --estate-accent-foreground: 210 40% 98%;
  }

  .dark {
    --background: 220 14% 10%;
    --foreground: 210 40% 98%;

    --card: 220 14% 10%;
    --card-foreground: 210 40% 98%;

    --popover: 220 14% 10%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Real estate specific colors */
    --estate: 210 40% 98%;
    --estate-foreground: 222.2 47.4% 11.2%;
    --estate-muted: 217.2 32.6% 17.5%;
    --estate-muted-foreground: 215 20.2% 65.1%;
    --estate-accent: 217 76% 56%;
    --estate-accent-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-light tracking-tight;
  }

  h1 {
    @apply text-4xl sm:text-5xl md:text-6xl;
  }

  h2 {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  h3 {
    @apply text-2xl sm:text-3xl;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Page transitions */
  .page-transition-enter {
    @apply opacity-0;
  }
  
  .page-transition-enter-active {
    @apply opacity-100 transition-opacity duration-300;
  }
  
  .page-transition-exit {
    @apply opacity-100;
  }
  
  .page-transition-exit-active {
    @apply opacity-0 transition-opacity duration-300;
  }
  
  /* Glassmorphism */
  .glass {
    @apply bg-white/70 dark:bg-black/70 backdrop-blur-md border border-white/20 dark:border-black/20;
  }
}

@layer components {
  .container-custom {
    @apply w-full max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .btn-estate {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors
    bg-estate text-estate-foreground hover:bg-estate/90 focus:outline-none focus:ring-2 focus:ring-estate-accent focus:ring-offset-2
    disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-estate-outline {
    @apply inline-flex items-center justify-center rounded-lg border border-estate px-4 py-2 text-sm font-medium transition-colors
    bg-transparent text-estate hover:bg-estate/10 focus:outline-none focus:ring-2 focus:ring-estate-accent focus:ring-offset-2
    disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-estate-ghost {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors
    bg-transparent text-estate hover:bg-estate/10 focus:outline-none focus:ring-2 focus:ring-estate-accent focus:ring-offset-2
    disabled:pointer-events-none disabled:opacity-50;
  }
}
